using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة الأقسام في المؤسسة
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class DepartmentsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILoggingService _loggingService;
        private readonly ILogger<DepartmentsController> _logger;

        public DepartmentsController(TasksDbContext context, ILoggingService loggingService, ILogger<DepartmentsController> logger)
        {
            _context = context;
            _loggingService = loggingService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع الأقسام مع دعم Pagination والبحث
        /// </summary>
        /// <param name="page">رقم الصفحة (افتراضي: 1)</param>
        /// <param name="pageSize">حجم الصفحة (افتراضي: 50)</param>
        /// <param name="search">نص البحث</param>
        /// <param name="searchQuery">نص البحث (بديل)</param>
        /// <param name="orderBy">حقل الترتيب</param>
        /// <param name="sortBy">حقل الترتيب (بديل)</param>
        /// <param name="orderDirection">اتجاه الترتيب (ASC/DESC)</param>
        /// <param name="sortOrder">اتجاه الترتيب (بديل)</param>
        /// <param name="isActive">فلتر حسب الحالة النشطة</param>
        /// <param name="managerId">فلتر حسب المدير</param>
        /// <returns>قائمة مقسمة من الأقسام</returns>
        /// <response code="200">إرجاع قائمة الأقسام مع معلومات Pagination</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<object>> GetDepartments(
            int page = 1,
            int pageSize = 50,
            string? search = null,
            string? searchQuery = null,
            string? orderBy = null,
            string? sortBy = null,
            string orderDirection = "ASC",
            string? sortOrder = null,
            bool? isActive = null,
            int? managerId = null)
        {
            try
            {
                // توحيد معاملات البحث والترتيب
                var searchTerm = search ?? searchQuery;
                var sortField = orderBy ?? sortBy ?? "Name";
                var sortDir = orderDirection ?? sortOrder ?? "ASC";

                // بناء الاستعلام الأساسي
                var query = _context.Departments
                    .Include(d => d.Manager)
                    .AsQueryable();

                // تطبيق فلتر الحالة النشطة (افتراضي: true)
                if (isActive.HasValue)
                    query = query.Where(d => d.IsActive == isActive.Value);
                else
                    query = query.Where(d => d.IsActive); // افتراضي: الأقسام النشطة فقط

                // تطبيق البحث
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(d =>
                        d.Name.Contains(searchTerm) ||
                        (d.Description != null && d.Description.Contains(searchTerm)) ||
                        (d.Manager != null && d.Manager.Name.Contains(searchTerm)));
                }

                // تطبيق الفلاتر
                if (managerId.HasValue)
                    query = query.Where(d => d.ManagerId == managerId.Value);

                // حساب إجمالي السجلات
                var totalRecords = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);

                // تطبيق الترتيب
                query = ApplyDepartmentSorting(query, sortField, sortDir);

                // تطبيق Pagination
                var departments = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // إرجاع النتيجة بتنسيق موحد
                var result = new
                {
                    data = departments,
                    totalRecords = totalRecords,
                    totalPages = totalPages,
                    currentPage = page,
                    pageSize = pageSize,
                    hasNextPage = page < totalPages,
                    hasPreviousPage = page > 1
                };

                // تسجيل عملية البحث والاستعلام في الأقسام
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    var searchDetails = new Dictionary<string, object>
                    {
                        ["page"] = page,
                        ["page_size"] = pageSize,
                        ["search_term"] = searchTerm ?? "",
                        ["is_active_filter"] = isActive?.ToString() ?? "",
                        ["manager_filter"] = managerId ?? 0,
                        ["sort_field"] = sortField,
                        ["sort_direction"] = sortDir
                    };

                    await _loggingService.LogActivityAsync(
                        "search",
                        "department",
                        0,
                        currentUserId,
                        $"بحث في الأقسام مع الفلاتر المطبقة",
                        newValue: System.Text.Json.JsonSerializer.Serialize(searchDetails));
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                // Log the error (in production, use proper logging)
                Console.WriteLine($"Error getting departments: {ex.Message}");
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب الأقسام",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get a specific department by ID
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <returns>Department details</returns>
        /// <response code="200">Returns the department</response>
        /// <response code="404">If the department is not found</response>
        /// <response code="500">If there is a server error</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<Department>> GetDepartment(int id)
        {
            try
            {
                // تحميل القسم مع المدير والأقسام الفرعية
                var department = await _context.Departments
                    .Include(d => d.Manager)
                    .Include(d => d.Children.Where(c => c.IsActive))
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (department == null)
                {
                    return NotFound();
                }

                return Ok(department);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في الحصول على القسم: {ex.Message}");
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب القسم",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Update a department
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <param name="department">Updated department data</param>
        /// <returns>No content</returns>
        /// <response code="204">Department updated successfully</response>
        /// <response code="400">Invalid request</response>
        /// <response code="404">Department not found</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutDepartment(int id, Department department)
        {
            if (id != department.Id)
            {
                return BadRequest();
            }

            _context.Entry(department).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();

                // تسجيل تحديث القسم
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogCrudOperationAsync(
                        "UPDATE",
                        "department",
                        id,
                        currentUserId,
                        department.Name,
                        new Dictionary<string, object>
                        {
                            ["name"] = department.Name ?? "",
                            ["description"] = department.Description ?? "",
                            ["manager_id"] = department.ManagerId ?? 0,
                            ["parent_id"] = department.ParentId ?? 0,
                            ["is_active"] = department.IsActive
                        },
                        HttpContext.Connection.RemoteIpAddress?.ToString());
                }
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DepartmentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// Create a new department
        /// </summary>
        /// <param name="department">Department data</param>
        /// <returns>Created department</returns>
        /// <response code="201">Department created successfully</response>
        /// <response code="400">Invalid request</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Department>> PostDepartment(Department department)
        {
            department.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            department.IsActive = true;
            
            _context.Departments.Add(department);
            await _context.SaveChangesAsync();

            // تسجيل إنشاء القسم
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogCrudOperationAsync(
                    "CREATE",
                    "department",
                    department.Id,
                    currentUserId,
                    department.Name,
                    new Dictionary<string, object>
                    {
                        ["name"] = department.Name ?? "",
                        ["description"] = department.Description ?? "",
                        ["manager_id"] = department.ManagerId ?? 0,
                        ["parent_id"] = department.ParentId ?? 0,
                        ["is_active"] = department.IsActive
                    },
                    HttpContext.Connection.RemoteIpAddress?.ToString());
            }

            return CreatedAtAction("GetDepartment", new { id = department.Id }, department);
        }

        /// <summary>
        /// Delete a department (deactivate)
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <returns>No content</returns>
        /// <response code="204">Department deactivated successfully</response>
        /// <response code="404">Department not found</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteDepartment(int id)
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null)
            {
                return NotFound();
            }

            department.IsActive = false;
            await _context.SaveChangesAsync();

            // تسجيل إلغاء تفعيل القسم
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogCrudOperationAsync(
                    "DELETE",
                    "department",
                    id,
                    currentUserId,
                    department.Name,
                    new Dictionary<string, object>
                    {
                        ["deactivated_at"] = DateTimeOffset.UtcNow.ToString(),
                        ["soft_delete"] = true
                    },
                    HttpContext.Connection.RemoteIpAddress?.ToString());
            }

            return NoContent();
        }

        /// <summary>
        /// Get users in a department
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <returns>List of users in the department</returns>
        /// <response code="200">Returns the list of users</response>
        /// <response code="404">Department not found</response>
        [HttpGet("{id}/assign-users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<User>>> GetDepartmentUsers(int id)
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null)
            {
                return NotFound();
            }

            var users = await _context.Users
                .Where(u => u.DepartmentId == id && u.IsActive && !u.IsDeleted)
                .Select(u => new User
                {
                    Id = u.Id,
                    Name = u.Name,
                    Email = u.Email,
                    Username = u.Username,
                    ProfileImage = u.ProfileImage, // تأكد من جلب الصورة الشخصية
                    DepartmentId = u.DepartmentId,
                    RoleId = u.RoleId,
                    IsActive = u.IsActive,
                    IsOnline = u.IsOnline,
                    CreatedAt = u.CreatedAt,
                    LastLogin = u.LastLogin,
                    LastSeen = u.LastSeen,
                    IsDeleted = u.IsDeleted
                })
                .ToListAsync();

            // تسجيل لفحص الصور الشخصية
            //foreach (var user in users)
            //{
                
            //    Console.WriteLine($"👤 مستخدم {user.Name} (ID: {user.Id}) - صورة شخصية: {user.ProfileImage ?? "فارغة"}");
            //}

            return users;
        }

        /// <summary>
        /// Get tasks in a department
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <returns>List of tasks in the department</returns>
        /// <response code="200">Returns the list of tasks</response>
        /// <response code="404">Department not found</response>
        [HttpGet("{id}/tasks")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetDepartmentTasks(int id)
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null)
            {
                return NotFound();
            }

            var tasks = await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Where(t => t.DepartmentId == id && !t.IsDeleted)
                .ToListAsync();

            return tasks;
        }

        /// <summary>
        /// Get statistics for all departments
        /// </summary>
        /// <returns>Statistics for all departments</returns>
        /// <response code="200">Returns departments statistics</response>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetDepartmentsStatistics()
        {
            try
            {
                var totalDepartments = await _context.Departments.CountAsync(d => d.IsActive);

                // Get departments with user and task counts
                var departmentStats = await _context.Departments
                    .Where(d => d.IsActive)
                    .Select(d => new
                    {
                        DepartmentId = d.Id,
                        DepartmentName = d.Name,
                        TotalUsers = d.Users.Count(u => !u.IsDeleted),
                        ActiveUsers = d.Users.Count(u => u.IsActive && !u.IsDeleted),
                        TotalTasks = d.Tasks.Count(t => !t.IsDeleted),
                        CompletedTasks = d.Tasks.Count(t => !t.IsDeleted && t.CompletedAt.HasValue),
                        PendingTasks = d.Tasks.Count(t => !t.IsDeleted && !t.CompletedAt.HasValue)
                    })
                    .ToListAsync();

                var totalUsers = await _context.Users.CountAsync(u => !u.IsDeleted);
                var totalTasks = await _context.Tasks.CountAsync(t => !t.IsDeleted);

                return Ok(new
                {
                    TotalDepartments = totalDepartments,
                    TotalUsers = totalUsers,
                    TotalTasks = totalTasks,
                    DepartmentStats = departmentStats
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting departments statistics: {ex.Message}");
                return StatusCode(500, new { error = "خطأ في الخادم أثناء جلب إحصائيات الأقسام", details = ex.Message });
            }
        }

        /// <summary>
        /// Get statistics for a specific department
        /// </summary>
        /// <param name="id">Department ID</param>
        /// <returns>Statistics for the department</returns>
        /// <response code="200">Returns department statistics</response>
        /// <response code="404">Department not found</response>
        [HttpGet("{id}/statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> GetDepartmentStatistics(int id)
        {
            try
            {
                var department = await _context.Departments
                    .Include(d => d.Users)
                    .Include(d => d.Tasks)
                    .FirstOrDefaultAsync(d => d.Id == id && d.IsActive);

                if (department == null)
                {
                    return NotFound(new { error = "القسم غير موجود" });
                }

                var totalUsers = department.Users.Count(u => !u.IsDeleted);
                var activeUsers = department.Users.Count(u => u.IsActive && !u.IsDeleted);
                var totalTasks = department.Tasks.Count(t => !t.IsDeleted);
                var completedTasks = department.Tasks.Count(t => !t.IsDeleted && t.CompletedAt.HasValue);
                var pendingTasks = totalTasks - completedTasks;

                // Task statistics by status
                var tasksByStatus = await _context.Tasks
                    .Where(t => t.DepartmentId == id && !t.IsDeleted)
                    .GroupBy(t => t.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync();

                // Task statistics by priority
                var tasksByPriority = await _context.Tasks
                    .Where(t => t.DepartmentId == id && !t.IsDeleted)
                    .GroupBy(t => t.Priority)
                    .Select(g => new { Priority = g.Key, Count = g.Count() })
                    .ToListAsync();

                return Ok(new
                {
                    DepartmentId = department.Id,
                    DepartmentName = department.Name,
                    TotalUsers = totalUsers,
                    ActiveUsers = activeUsers,
                    TotalTasks = totalTasks,
                    CompletedTasks = completedTasks,
                    PendingTasks = pendingTasks,
                    CompletionRate = totalTasks > 0 ? Math.Round((double)completedTasks / totalTasks * 100, 2) : 0,
                    TasksByStatus = tasksByStatus,
                    TasksByPriority = tasksByPriority
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting department statistics: {ex.Message}");
                return StatusCode(500, new { error = "خطأ في الخادم أثناء جلب إحصائيات القسم", details = ex.Message });
            }
        }

        private bool DepartmentExists(int id)
        {
            return _context.Departments.Any(e => e.Id == id);
        }

        /// <summary>
        /// تطبيق الترتيب على استعلام الأقسام
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="sortField">حقل الترتيب</param>
        /// <param name="sortDirection">اتجاه الترتيب</param>
        /// <returns>الاستعلام مع الترتيب المطبق</returns>
        private IQueryable<Department> ApplyDepartmentSorting(IQueryable<Department> query, string sortField, string sortDirection)
        {
            var isDescending = sortDirection.ToUpper() == "DESC";

            return sortField.ToLower() switch
            {
                "id" => isDescending ? query.OrderByDescending(d => d.Id) : query.OrderBy(d => d.Id),
                "name" => isDescending ? query.OrderByDescending(d => d.Name) : query.OrderBy(d => d.Name),
                "description" => isDescending ? query.OrderByDescending(d => d.Description) : query.OrderBy(d => d.Description),
                "managerid" => isDescending ? query.OrderByDescending(d => d.ManagerId) : query.OrderBy(d => d.ManagerId),
                "isactive" => isDescending ? query.OrderByDescending(d => d.IsActive) : query.OrderBy(d => d.IsActive),
                "createdat" => isDescending ? query.OrderByDescending(d => d.CreatedAt) : query.OrderBy(d => d.CreatedAt),
                _ => query.OrderBy(d => d.Name) // الترتيب الافتراضي
            };
        }

        // تفعيل قسم
        [HttpPut("{id}/activate")]
        public async Task<IActionResult> ActivateDepartment(int id)
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null) return NotFound();
            department.IsActive = true;
            await _context.SaveChangesAsync();

            // تسجيل تفعيل القسم
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "activate",
                    "department",
                    id,
                    currentUserId,
                    $"تفعيل القسم: {department.Name}");
            }

            return NoContent();
        }

        // إلغاء تفعيل قسم
        [HttpPut("{id}/deactivate")]
        public async Task<IActionResult> DeactivateDepartment(int id)
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null) return NotFound();
            department.IsActive = false;
            await _context.SaveChangesAsync();

            // تسجيل إلغاء تفعيل القسم
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "deactivate",
                    "department",
                    id,
                    currentUserId,
                    $"إلغاء تفعيل القسم: {department.Name}");
            }

            return NoContent();
        }

        // نقل موظف إلى قسم آخر
        [HttpPut("transfer-employee")]
        public async Task<IActionResult> TransferEmployee([FromBody] TransferEmployeeDto dto)
        {
            var user = await _context.Users.FindAsync(dto.EmployeeId);
            if (user == null) return NotFound("الموظف غير موجود");
            user.DepartmentId = dto.ToDepartmentId;
            await _context.SaveChangesAsync();
            return NoContent();
        }
        public class TransferEmployeeDto { public int EmployeeId { get; set; } public int ToDepartmentId { get; set; } }

        // نقل مهمة إلى قسم آخر
        [HttpPut("transfer-task")]
        public async Task<IActionResult> TransferTask([FromBody] TransferTaskDto dto)
        {
            var task = await _context.Tasks.FindAsync(dto.TaskId);
            if (task == null) return NotFound("المهمة غير موجودة");
            task.DepartmentId = dto.ToDepartmentId;
            await _context.SaveChangesAsync();
            return NoContent();
        }
        public class TransferTaskDto { public int TaskId { get; set; } public int ToDepartmentId { get; set; } }

        // دمج قسمين
        [HttpPost("merge")]
        public async Task<IActionResult> MergeDepartments([FromBody] MergeDepartmentsDto dto)
        {
            var fromDept = await _context.Departments.FindAsync(dto.FromDepartmentId);
            var toDept = await _context.Departments.FindAsync(dto.ToDepartmentId);
            if (fromDept == null || toDept == null) return NotFound("قسم غير موجود");

            // 1. تحديث الأقسام الفرعية لتتبع القسم الهدف
            var subDepartments = _context.Departments.Where(d => d.ParentId == fromDept.Id);
            foreach (var subDept in subDepartments)
                subDept.ParentId = toDept.Id;

            // 2. نقل المستخدمين
            var users = _context.Users.Where(u => u.DepartmentId == fromDept.Id);
            foreach (var user in users)
                user.DepartmentId = toDept.Id;

            // 3. نقل المهام
            var tasks = _context.Tasks.Where(t => t.DepartmentId == fromDept.Id);
            foreach (var task in tasks)
                task.DepartmentId = toDept.Id;

            // 4. حذف القسم المدموج بعد نقل كل العلاقات
            _context.Departments.Remove(fromDept);
            await _context.SaveChangesAsync();
            return NoContent();
        }
        public class MergeDepartmentsDto { public int FromDepartmentId { get; set; } public int ToDepartmentId { get; set; } }

        // تصدير بيانات الأقسام
        [HttpGet("export")]
        public async Task<IActionResult> ExportDepartments()
        {
            var departments = await _context.Departments.Include(d => d.Users).Include(d => d.Tasks).ToListAsync();
            return Ok(departments); // يمكنك تخصيص البيانات حسب الحاجة
        }

        // استيراد بيانات الأقسام
        [HttpPost("import")]
        public async Task<IActionResult> ImportDepartments([FromBody] List<Department> departments)
        {
            foreach (var dept in departments)
            {
                if (!_context.Departments.Any(d => d.Id == dept.Id))
                    _context.Departments.Add(dept);
            }
            await _context.SaveChangesAsync();
            return Ok();
        }

        /// <summary>
        /// جلب التسلسل الهرمي للأقسام
        /// </summary>
        /// <returns>قائمة الأقسام مع التسلسل الهرمي</returns>
        [HttpGet("hierarchy")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<Department>>> GetDepartmentHierarchy()
        {
            try
            {
                // جلب جميع الأقسام النشطة مع المديرين
                var allDepartments = await _context.Departments
                    .Include(d => d.Manager)
                    .Where(d => d.IsActive)
                    .OrderBy(d => d.Level)
                    .ThenBy(d => d.SortOrder)
                    .ThenBy(d => d.Name)
                    .ToListAsync();

                // Debug: طباعة جميع الأقسام من قاعدة البيانات
                Console.WriteLine("=== DEBUG: جميع الأقسام من قاعدة البيانات ===");
                foreach (var dept in allDepartments)
                {
                    Console.WriteLine($"القسم: {dept.Name} (ID: {dept.Id}, Parent: {dept.ParentId}, Level: {dept.Level})");
                }
                Console.WriteLine("=== نهاية DEBUG قاعدة البيانات ===");

                // بناء التسلسل الهرمي - إرجاع الأقسام الرئيسية فقط مع أطفالها
                var hierarchy = BuildHierarchy(allDepartments);
                
                // Debug: طباعة التسلسل الهرمي
                Console.WriteLine("=== DEBUG: التسلسل الهرمي المرسل ===");
                foreach (var dept in hierarchy)
                {
                    Console.WriteLine($"القسم الرئيسي: {dept.Name} (ID: {dept.Id})");
                    if (dept.Children != null)
                    {
                        foreach (var child in dept.Children)
                        {
                            Console.WriteLine($"  القسم الفرعي: {child.Name} (ID: {child.Id}, Parent: {child.ParentId})");
                        }
                    }
                }
                Console.WriteLine("=== نهاية DEBUG ===");
                
                return Ok(hierarchy);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في جلب التسلسل الهرمي: {ex.Message}");
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب التسلسل الهرمي للأقسام",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// بناء التسلسل الهرمي للأقسام
        /// </summary>
        /// <param name="allDepartments">جميع الأقسام</param>
        /// <returns>قائمة الأقسام الرئيسية مع أطفالها</returns>
        private List<Department> BuildHierarchy(List<Department> allDepartments)
        {
            // إنشاء نسخ من الأقسام لتجنب تعديل الكائنات الأصلية
            var departmentDict = allDepartments.ToDictionary(d => d.Id, d => new Department
            {
                Id = d.Id,
                Name = d.Name,
                Description = d.Description,
                ManagerId = d.ManagerId,
                IsActive = d.IsActive,
                CreatedAt = d.CreatedAt,
                ParentId = d.ParentId,
                Level = d.Level,
                SortOrder = d.SortOrder,
                Manager = d.Manager,
                Children = new List<Department>()
            });

            var rootDepartments = new List<Department>();

            // المرحلة الأولى: تحديد الأقسام الرئيسية
            Console.WriteLine("=== DEBUG: بناء الأقسام الرئيسية ===");
            foreach (var department in allDepartments)
            {
                if (department.ParentId == null)
                {
                    Console.WriteLine($"إضافة قسم رئيسي: {department.Name} (ID: {department.Id})");
                    rootDepartments.Add(departmentDict[department.Id]);
                }
            }

            // المرحلة الثانية: بناء العلاقات الهرمية
            Console.WriteLine("=== DEBUG: بناء العلاقات الهرمية ===");
            foreach (var department in allDepartments)
            {
                if (department.ParentId != null && departmentDict.ContainsKey(department.ParentId.Value))
                {
                    var parent = departmentDict[department.ParentId.Value];
                    var child = departmentDict[department.Id];
                    
                    Console.WriteLine($"إضافة قسم فرعي: {child.Name} (ID: {child.Id}) تحت الوالد: {parent.Name} (ID: {parent.Id})");
                    ((List<Department>)parent.Children).Add(child);
                }
            }

            Console.WriteLine($"=== DEBUG: عدد الأقسام الرئيسية النهائية: {rootDepartments.Count} ===");
            return rootDepartments;
        }

        // إعادة ترتيب الأقسام
        [HttpPut("reorder")]
        public async Task<IActionResult> ReorderDepartments([FromBody] ReorderDepartmentsDto dto)
        {
            foreach (var order in dto.DepartmentOrders)
            {
                var dept = await _context.Departments.FindAsync(order.DepartmentId);
                if (dept != null) dept.SortOrder = order.Order;
            }
            await _context.SaveChangesAsync();
            return NoContent();
        }
        public class ReorderDepartmentsDto { public List<DepartmentOrder> DepartmentOrders { get; set; } }
        public class DepartmentOrder { public int DepartmentId { get; set; } public int Order { get; set; } }

        // جلب الأقسام الرئيسية
        [HttpGet("parents")]
        public async Task<IActionResult> GetParentDepartments()
        {
            var parents = await _context.Departments.Where(d => d.ParentId == null).ToListAsync();
            return Ok(parents);
        }

        /// <summary>
        /// جلب الأقسام الفرعية لقسم محدد
        /// </summary>
        /// <param name="parentId">معرف القسم الأب</param>
        /// <returns>قائمة الأقسام الفرعية</returns>
        [HttpGet("{parentId}/children")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<Department>>> GetSubDepartments(int parentId)
        {
            try
            {
                // التحقق من وجود القسم الأب
                var parentDepartment = await _context.Departments.FindAsync(parentId);
                if (parentDepartment == null)
                {
                    return NotFound($"القسم الأب بالمعرف {parentId} غير موجود");
                }

                // جلب الأقسام الفرعية النشطة مع المديرين
                var children = await _context.Departments
                    .Include(d => d.Manager)
                    .Where(d => d.ParentId == parentId && d.IsActive)
                    .OrderBy(d => d.SortOrder)
                    .ThenBy(d => d.Name)
                    .ToListAsync();

                return Ok(children);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في جلب الأقسام الفرعية: {ex.Message}");
                return StatusCode(500, new
                {
                    success = false,
                    message = "حدث خطأ أثناء جلب الأقسام الفرعية",
                    error = ex.Message
                });
            }
        }

        // تعيين مستخدمين لقسم (POST)
        [HttpPost("{id}/assign-users")]
        public async Task<IActionResult> AssignUsersToDepartment(int id, [FromBody] AssignUsersDto dto)
        {
            var users = _context.Users.Where(u => dto.UserIds.Contains(u.Id));
            foreach (var user in users) user.DepartmentId = id;
            await _context.SaveChangesAsync();
            return Ok(new { success = true });
        }
        public class AssignUsersDto { public List<int> UserIds { get; set; } }

        /// <summary>
        /// نقل قسم إلى قسم آخر (تغيير الأب)
        /// </summary>
        /// <param name="id">معرف القسم المراد نقله</param>
        /// <param name="dto">كائن يحتوي على معرف القسم الأب الجديد</param>
        /// <returns>No content</returns>
        /// <response code="204">تم النقل بنجاح</response>
        /// <response code="404">القسم غير موجود</response>
        [HttpPut("{id}/move")]
        public async Task<IActionResult> MoveDepartment(int id, [FromBody] MoveDepartmentDto dto)
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null)
                return NotFound();

            // التأكد من أن القسم الهدف موجود (أو null لجعله رئيسي)
            if (dto.NewParentId.HasValue && !_context.Departments.Any(d => d.Id == dto.NewParentId.Value))
                return NotFound("القسم الأب الجديد غير موجود");

            department.ParentId = dto.NewParentId;
            await _context.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// البحث الشامل في الأقسام - للبحث الموحد
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="limit">عدد النتائج المطلوبة (افتراضي: 50)</param>
        /// <returns>قائمة الأقسام المطابقة</returns>
        /// <response code="200">إرجاع قائمة الأقسام المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> SearchDepartments(
            [FromQuery] string searchTerm,
            [FromQuery] int limit = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return Ok(new List<object>());
                }

                var currentUserId = GetCurrentUserId();

                // تسجيل عملية البحث الشامل
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "comprehensive_search",
                        "department",
                        0,
                        currentUserId,
                        $"البحث الشامل في الأقسام بالمصطلح: '{searchTerm}'");
                }

                // البحث الشامل في جميع حقول الأقسام
                var query = _context.Departments
                    .Include(d => d.Manager)
                    .Include(d => d.Parent)
                    .Where(d => d.IsActive); // الأقسام النشطة فقط

                // البحث الشامل في جميع الحقول النصية
                query = query.Where(d =>
                    // البحث في اسم القسم
                    d.Name.Contains(searchTerm) ||
                    // البحث في وصف القسم
                    (d.Description != null && d.Description.Contains(searchTerm)) ||
                    // البحث في اسم المدير
                    (d.Manager != null && d.Manager.Name.Contains(searchTerm)) ||
                    // البحث في اسم القسم الأب
                    (d.Parent != null && d.Parent.Name.Contains(searchTerm))
                );

                // تطبيق الحد الأقصى للنتائج والترتيب
                var departments = await query
                    .OrderBy(d => d.Level)
                    .ThenBy(d => d.SortOrder)
                    .ThenBy(d => d.Name)
                    .Take(Math.Min(limit, 100)) // حد أقصى 100 نتيجة
                    .Select(d => new {
                        d.Id,
                        d.Name,
                        d.Description,
                        d.Level,
                        d.IsActive,
                        ManagerName = d.Manager != null ? d.Manager.Name : null,
                        ParentName = d.Parent != null ? d.Parent.Name : null,
                        d.CreatedAt
                    })
                    .ToListAsync();

                _logger.LogInformation("البحث الشامل في الأقسام: تم العثور على {Count} نتيجة للمصطلح '{SearchTerm}'",
                    departments.Count, searchTerm);

                return Ok(departments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث الشامل في الأقسام بالمصطلح: {SearchTerm}", searchTerm);
                return StatusCode(500, "خطأ في الخادم أثناء البحث");
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي من JWT token
        /// </summary>
        private int GetCurrentUserId()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value ??
                                 User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        public class MoveDepartmentDto { public int? NewParentId { get; set; } }
    }
}
