import 'services/unified_search_service.dart';
import 'models/search_models.dart';

/// اختبار بسيط للبحث الشامل
class SimpleSearchTest {
  static Future<void> testSearch() async {
    try {
      
      final searchService = UnifiedSearchService();
      
      // اختبار البحث بكلمة "test"
      final results = await searchService.search(
        query: 'test',
        types: SearchResultType.values,
        limit: 5,
      );
      
      print('📊 نتائج الاختبار:');
      print('- إجمالي النتائج: ${results.length}');
      
      // عرض النتائج حسب النوع
      final typeGroups = <SearchResultType, List<SearchResult>>{};
      for (final result in results) {
        typeGroups[result.type] ??= [];
        typeGroups[result.type]!.add(result);
      }
      
      for (final entry in typeGroups.entries) {
        final typeName = _getTypeName(entry.key);
        print('- $typeName: ${entry.value.length} نتيجة');
        for (final result in entry.value) {
          print('  • ${result.title}');
        }
      }
      
      if (results.isEmpty) {
        print('⚠️ لم يتم العثور على نتائج');
      } else {
        print('✅ اختبار البحث نجح!');
      }
      
    } catch (e) {
      print('❌ فشل اختبار البحث: $e');
    }
  }
  
  static String _getTypeName(SearchResultType type) {
    switch (type) {
      case SearchResultType.task:
        return 'المهام';
      case SearchResultType.message:
        return 'الرسائل';
      case SearchResultType.user:
        return 'المستخدمين';
      case SearchResultType.document:
        return 'الوثائق';
      case SearchResultType.event:
        return 'الأحداث';
      case SearchResultType.report:
        return 'التقارير';
      case SearchResultType.attachment:
        return 'المرفقات';
      case SearchResultType.messageAttachment:
        return 'مرفقات الرسائل';
      case SearchResultType.comment:
        return 'التعليقات';
      case SearchResultType.department:
        return 'الأقسام';
      case SearchResultType.role:
        return 'الأدوار';
      case SearchResultType.other:
        return 'أخرى';
    }
  }
}
