import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/search_models.dart';
import 'api/messages_api_service.dart';
import 'api/users_api_service.dart';
import 'api/archive_documents_api_service.dart';
import 'api/calendar_events_api_service.dart';
import 'api/reports_api_service.dart';
import 'api/task_api_service.dart';
import 'api/api_service.dart';
import '../controllers/auth_controller.dart';

/// خدمة البحث الموحد
class UnifiedSearchService {
  final MessagesApiService _messageService = MessagesApiService();
  final UsersApiService _userService = UsersApiService();
  final ArchiveDocumentsApiService _documentService = ArchiveDocumentsApiService();
  final CalendarEventsApiService _eventService = CalendarEventsApiService();
  final ReportsApiService _reportService = ReportsApiService();

  /// البحث الموحد
  Future<List<SearchResult>> search({
    required String query,
    List<SearchResultType>? types,
    int limit = 10,
    bool includeDeleted = false,
  }) async {
    final results = <SearchResult>[];
    final searchTypes = types ?? SearchResultType.values;

    // تنفيذ البحث بشكل متوازي
    final futures = <Future>[];

    if (searchTypes.contains(SearchResultType.task)) {
      futures.add(_searchTasks(query, limit).then((taskResults) {
        results.addAll(taskResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.message)) {
      futures.add(_searchMessages(query, limit).then((messageResults) {
        results.addAll(messageResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.user)) {
      futures.add(_searchUsers(query, limit).then((userResults) {
        results.addAll(userResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.document)) {
      futures.add(_searchDocuments(query, limit).then((documentResults) {
        results.addAll(documentResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.event)) {
      futures.add(_searchEvents(query, limit).then((eventResults) {
        results.addAll(eventResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.report)) {
      futures.add(_searchReports(query, limit).then((reportResults) {
        results.addAll(reportResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.attachment)) {
      futures.add(_searchAttachments(query, limit).then((attachmentResults) {
        results.addAll(attachmentResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.comment)) {
      futures.add(_searchComments(query, limit).then((commentResults) {
        results.addAll(commentResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.messageAttachment)) {
      futures.add(_searchMessageAttachments(query, limit).then((messageAttachmentResults) {
        results.addAll(messageAttachmentResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.department)) {
      futures.add(_searchDepartments(query, limit).then((departmentResults) {
        results.addAll(departmentResults);
      }));
    }

    if (searchTypes.contains(SearchResultType.role)) {
      futures.add(_searchRoles(query, limit).then((roleResults) {
        results.addAll(roleResults);
      }));
    }

    // انتظار انتهاء جميع عمليات البحث
    await Future.wait(futures);

    // ترتيب النتائج حسب الصلة
    results.sort((a, b) => b.relevance.compareTo(a.relevance));

    return results;
  }

  /// البحث في المهام - محسن للبحث المباشر في قاعدة البيانات
  Future<List<SearchResult>> _searchTasks(String query, int limit) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUserId = authController.currentUser.value?.id;

      if (currentUserId == null) {
        debugPrint('لا يوجد مستخدم مسجل دخول للبحث في المهام');
        return [];
      }

      // استخدام TaskApiService للبحث المباشر في قاعدة البيانات
      final taskApiService = TaskApiService();

      // استدعاء دالة البحث الجديدة
      final tasks = await taskApiService.searchTasksComprehensive(
        query,
        userId: currentUserId,
        limit: limit
      );

      return tasks.map((task) => SearchResult(
        id: task.id.toString(),
        type: SearchResultType.task,
        title: task.title,
        subtitle: task.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(task.createdAt > 1000000000000 ? task.createdAt : task.createdAt * 1000),
        relevance: _calculateRelevance(query, task.title),
        data: task,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المهام: $e');
      return [];
    }
  }

  /// البحث في الرسائل
  Future<List<SearchResult>> _searchMessages(String query, int limit) async {
    try {
      final messages = await _messageService.searchMessages(query);
      return messages.take(limit).map((message) => SearchResult(
        id: message.id.toString(),
        type: SearchResultType.message,
        title: 'رسالة من ${message.senderName ?? 'مجهول'}',
        subtitle: message.content,
        date: DateTime.fromMillisecondsSinceEpoch(message.createdAt > 1000000000000 ? message.createdAt : message.createdAt * 1000),
        relevance: _calculateRelevance(query, message.content),
        data: message,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الرسائل: $e');
      return [];
    }
  }

  /// البحث في المستخدمين
  Future<List<SearchResult>> _searchUsers(String query, int limit) async {
    try {
      final users = await _userService.searchUsers(query);
      return users.take(limit).map((user) => SearchResult(
        id: user.id.toString(),
        type: SearchResultType.user,
        title: user.name,
        subtitle: user.email ?? 'غير محدد',
        date: DateTime.fromMillisecondsSinceEpoch(user.createdAt > 1000000000000 ? user.createdAt : user.createdAt * 1000),
        relevance: _calculateRelevance(query, user.name),
        data: user,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المستخدمين: $e');
      return [];
    }
  }

  /// البحث في الوثائق
  Future<List<SearchResult>> _searchDocuments(String query, int limit) async {
    try {
      final documents = await _documentService.searchDocuments(query);
      return documents.take(limit).map((document) => SearchResult(
        id: document.id.toString(),
        type: SearchResultType.document,
        title: document.title,
        subtitle: document.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(document.createdAt > 1000000000000 ? document.createdAt : document.createdAt * 1000),
        relevance: _calculateRelevance(query, document.title),
        data: document,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الوثائق: $e');
      return [];
    }
  }

  /// البحث في الأحداث
  Future<List<SearchResult>> _searchEvents(String query, int limit) async {
    try {
      final events = await _eventService.searchEvents(query);
      return events.take(limit).map((event) => SearchResult(
        id: event.id.toString(),
        type: SearchResultType.event,
        title: event.title,
        subtitle: event.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(event.startTime > 1000000000000 ? event.startTime : event.startTime * 1000),
        relevance: _calculateRelevance(query, event.title),
        data: event,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الأحداث: $e');
      return [];
    }
  }

  /// البحث في التقارير
  Future<List<SearchResult>> _searchReports(String query, int limit) async {
    try {
      final reports = await _reportService.searchReports(query);
      return reports.take(limit).map((report) => SearchResult(
        id: report.id.toString(),
        type: SearchResultType.report,
        title: report.title,
        subtitle: report.description ?? '',
        date: DateTime.fromMillisecondsSinceEpoch(report.createdAt > 1000000000000 ? report.createdAt : report.createdAt * 1000),
        relevance: _calculateRelevance(query, report.title),
        data: report,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في التقارير: $e');
      return [];
    }
  }

  /// البحث في المرفقات
  Future<List<SearchResult>> _searchAttachments(String query, int limit) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUserId = authController.currentUser.value?.id;

      if (currentUserId == null) {
        debugPrint('لا يوجد مستخدم مسجل دخول للبحث في المرفقات');
        return [];
      }

      // إنشاء instance من ApiService مباشرة
      final apiService = ApiService();
      final response = await apiService.get(
        '/api/Attachments/search',
        queryParams: {
          'searchTerm': query,
          'userId': currentUserId.toString(),
          'limit': limit.toString(),
        },
      );

      final attachmentsData = apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );

      return attachmentsData.map((attachment) => SearchResult(
        id: attachment['Id'].toString(),
        type: SearchResultType.attachment,
        title: attachment['FileName'] ?? 'مرفق',
        subtitle: 'المهمة: ${attachment['TaskTitle'] ?? 'غير محدد'} - النوع: ${attachment['FileType'] ?? 'غير محدد'}',
        date: DateTime.fromMillisecondsSinceEpoch(
          (attachment['UploadedAt'] as int? ?? 0) > 1000000000000
            ? attachment['UploadedAt']
            : (attachment['UploadedAt'] as int? ?? 0) * 1000
        ),
        relevance: _calculateRelevance(query, attachment['FileName'] ?? ''),
        data: attachment,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في المرفقات: $e');
      return [];
    }
  }

  /// البحث في التعليقات
  Future<List<SearchResult>> _searchComments(String query, int limit) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUserId = authController.currentUser.value?.id;

      if (currentUserId == null) {
        debugPrint('لا يوجد مستخدم مسجل دخول للبحث في التعليقات');
        return [];
      }

      // إنشاء instance من ApiService مباشرة
      final apiService = ApiService();
      final response = await apiService.get(
        '/api/TaskComments/search',
        queryParams: {
          'searchTerm': query,
          'userId': currentUserId.toString(),
          'limit': limit.toString(),
        },
      );

      final commentsData = apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );

      return commentsData.map((comment) => SearchResult(
        id: comment['Id'].toString(),
        type: SearchResultType.comment,
        title: 'تعليق من ${comment['AuthorName'] ?? 'مجهول'}',
        subtitle: 'المهمة: ${comment['TaskTitle'] ?? 'غير محدد'} - ${comment['Content'] ?? ''}',
        date: DateTime.fromMillisecondsSinceEpoch(
          (comment['CreatedAt'] as int? ?? 0) > 1000000000000
            ? comment['CreatedAt']
            : (comment['CreatedAt'] as int? ?? 0) * 1000
        ),
        relevance: _calculateRelevance(query, comment['Content'] ?? ''),
        data: comment,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في التعليقات: $e');
      return [];
    }
  }

  /// البحث في مرفقات الرسائل
  Future<List<SearchResult>> _searchMessageAttachments(String query, int limit) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUserId = authController.currentUser.value?.id;

      if (currentUserId == null) {
        debugPrint('لا يوجد مستخدم مسجل دخول للبحث في مرفقات الرسائل');
        return [];
      }

      // إنشاء instance من ApiService مباشرة
      final apiService = ApiService();
      final response = await apiService.get(
        '/api/Messages/attachments/search',
        queryParams: {
          'searchTerm': query,
          'userId': currentUserId.toString(),
          'limit': limit.toString(),
        },
      );

      final attachmentsData = apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );

      return attachmentsData.map((attachment) => SearchResult(
        id: attachment['Id'].toString(),
        type: SearchResultType.messageAttachment,
        title: attachment['FileName'] ?? 'مرفق رسالة',
        subtitle: 'المجموعة: ${attachment['GroupName'] ?? 'غير محدد'} - النوع: ${attachment['FileType'] ?? 'غير محدد'}',
        date: DateTime.fromMillisecondsSinceEpoch(
          (attachment['UploadedAt'] as int? ?? 0) > 1000000000000
            ? attachment['UploadedAt']
            : (attachment['UploadedAt'] as int? ?? 0) * 1000
        ),
        relevance: _calculateRelevance(query, attachment['FileName'] ?? ''),
        data: attachment,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في مرفقات الرسائل: $e');
      return [];
    }
  }

  /// البحث في الأقسام
  Future<List<SearchResult>> _searchDepartments(String query, int limit) async {
    try {
      // إنشاء instance من ApiService مباشرة
      final apiService = ApiService();
      final response = await apiService.get(
        '/api/Departments/search',
        queryParams: {
          'searchTerm': query,
          'limit': limit.toString(),
        },
      );

      final departmentsData = apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );

      return departmentsData.map((department) => SearchResult(
        id: department['Id'].toString(),
        type: SearchResultType.department,
        title: department['Name'] ?? 'قسم',
        subtitle: 'الوصف: ${department['Description'] ?? 'غير محدد'} - المدير: ${department['ManagerName'] ?? 'غير محدد'}',
        date: DateTime.fromMillisecondsSinceEpoch(
          (department['CreatedAt'] as int? ?? 0) > 1000000000000
            ? department['CreatedAt']
            : (department['CreatedAt'] as int? ?? 0) * 1000
        ),
        relevance: _calculateRelevance(query, department['Name'] ?? ''),
        data: department,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الأقسام: $e');
      return [];
    }
  }

  /// البحث في الأدوار
  Future<List<SearchResult>> _searchRoles(String query, int limit) async {
    try {
      // إنشاء instance من ApiService مباشرة
      final apiService = ApiService();
      final response = await apiService.get(
        '/api/Admin/roles/search',
        queryParams: {
          'searchTerm': query,
          'limit': limit.toString(),
        },
      );

      final rolesData = apiService.handleListResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );

      return rolesData.map((role) => SearchResult(
        id: role['Id'].toString(),
        type: SearchResultType.role,
        title: role['DisplayName'] ?? role['Name'] ?? 'دور',
        subtitle: 'الوصف: ${role['Description'] ?? 'غير محدد'} - المستوى: ${role['Level'] ?? 0} - المستخدمين: ${role['UsersCount'] ?? 0}',
        date: DateTime.fromMillisecondsSinceEpoch(
          (role['CreatedAt'] as int? ?? 0) > 1000000000000
            ? role['CreatedAt']
            : (role['CreatedAt'] as int? ?? 0) * 1000
        ),
        relevance: _calculateRelevance(query, role['DisplayName'] ?? role['Name'] ?? ''),
        data: role,
      )).toList();
    } catch (e) {
      debugPrint('خطأ في البحث في الأدوار: $e');
      return [];
    }
  }

  /// حساب درجة الصلة
  double _calculateRelevance(String query, String text) {
    if (text.isEmpty) return 0.0;
    
    final queryLower = query.toLowerCase();
    final textLower = text.toLowerCase();
    
    // إذا كان النص يحتوي على الاستعلام بالضبط
    if (textLower.contains(queryLower)) {
      // إذا كان النص يبدأ بالاستعلام
      if (textLower.startsWith(queryLower)) {
        return 1.0;
      }
      // إذا كان النص يحتوي على الاستعلام
      return 0.8;
    }
    
    // حساب التشابه بناءً على الكلمات المشتركة
    final queryWords = queryLower.split(' ');
    final textWords = textLower.split(' ');
    
    int matchingWords = 0;
    for (final queryWord in queryWords) {
      if (textWords.any((textWord) => textWord.contains(queryWord))) {
        matchingWords++;
      }
    }
    
    return matchingWords / queryWords.length * 0.6;
  }

  /// البحث الشامل في جميع الأنواع
  Future<List<SearchResult>> searchAll(String query, {int limit = 20}) async {
    return await search(
      query: query,
      types: [
        SearchResultType.task,
        SearchResultType.message,
        SearchResultType.user,
        SearchResultType.document,
        SearchResultType.event,
        SearchResultType.report,
        SearchResultType.attachment,
        SearchResultType.messageAttachment,
        SearchResultType.comment,
        SearchResultType.department,
        SearchResultType.role,
      ],
      limit: limit
    );
  }
}
