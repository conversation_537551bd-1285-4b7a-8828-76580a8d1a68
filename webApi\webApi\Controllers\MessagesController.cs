using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using webApi.Hubs;
using webApi.Models;
using webApi.Services;

namespace webApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[AllowAnonymous]
    public class MessagesController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly IHubContext<ChatHub> _chatHubContext;
        private readonly ILogger<MessagesController> _logger;
        private readonly INotificationService _notificationService;
        private readonly ILoggingService _loggingService;

        public MessagesController(TasksDbContext context, IHubContext<ChatHub> chatHubContext, ILogger<MessagesController> logger, INotificationService notificationService, ILoggingService loggingService)
        {
            _context = context;
            _chatHubContext = chatHubContext;
            _logger = logger;
            _notificationService = notificationService;
            _loggingService = loggingService;
        }

        // GET: api/Messages
        [HttpGet]
        //[AllowAnonymous]
        public async Task<ActionResult<IEnumerable<Message>>> GetMessages()
        {
            try
            {
                var messages = await _context.Messages
                    .Where(m => !m.IsDeleted)
                    .OrderByDescending(m => m.CreatedAt)
                    .Take(100) // Limit to prevent large result sets
                    .ToListAsync();

                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Message>> GetMessage(int id)
        {
            try
            {
                var message = await _context.Messages
                    .AsNoTracking()
                    .Where(m => m.Id == id && !m.IsDeleted)
                    .Select(m => new Message
                    {
                        Id = m.Id,
                        GroupId = m.GroupId,
                        SenderId = m.SenderId,
                        Content = m.Content,
                        ContentType = m.ContentType,
                        ReplyToMessageId = m.ReplyToMessageId,
                        CreatedAt = m.CreatedAt,
                        UpdatedAt = m.UpdatedAt,
                        IsDeleted = m.IsDeleted,
                        IsRead = m.IsRead,
                        IsPinned = m.IsPinned,
                        PinnedAt = m.PinnedAt,
                        PinnedBy = m.PinnedBy,
                        Priority = m.Priority,
                        IsMarkedForFollowUp = m.IsMarkedForFollowUp,
                        FollowUpAt = m.FollowUpAt,
                        MarkedForFollowUpBy = m.MarkedForFollowUpBy,
                        IsEdited = m.IsEdited,
                        ReceiverId = m.ReceiverId,
                        SentAt = m.SentAt
                    })
                    .FirstOrDefaultAsync();

                if (message == null)
                {
                    return NotFound();
                }

                return Ok(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting message {Id}: {Error}", id, ex.Message);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/group/5
        [HttpGet("group/{groupId}")]
        public async Task<ActionResult<IEnumerable<Message>>> GetGroupMessages(int groupId)
        {
            try
            {
                // التحقق من صحة معرف المجموعة
                if (groupId <= 0)
                {
                    _logger.LogWarning("Invalid group ID: {GroupId}", groupId);
                    return BadRequest("Invalid group ID");
                }

                // البحث عن المجموعة مع التحقق من القيم المطلوبة
                var group = await _context.ChatGroups
                    .Where(g => g.Id == groupId &&
                               g.CreatedBy > 0 &&
                               g.CreatedAt > 0 &&
                               !string.IsNullOrEmpty(g.Name))
                    .FirstOrDefaultAsync();

                if (group == null)
                {
                    _logger.LogWarning("Group not found or has invalid data: {GroupId}", groupId);
                    return NotFound("Group not found or has invalid data");
                }

                // جلب الرسائل مع التحقق من صحة البيانات وتعطيل Navigation Properties
                var messages = await _context.Messages
                    .AsNoTracking() // تعطيل التتبع لتحسين الأداء
                    .Where(m => m.GroupId == groupId &&
                               !m.IsDeleted &&
                               m.SenderId > 0 &&
                               m.CreatedAt > 0 &&
                               !string.IsNullOrEmpty(m.Content))
                    .OrderBy(m => m.CreatedAt)
                    .Select(m => new Message
                    {
                        Id = m.Id,
                        GroupId = m.GroupId,
                        SenderId = m.SenderId,
                        Content = m.Content,
                        ContentType = m.ContentType,
                        ReplyToMessageId = m.ReplyToMessageId,
                        CreatedAt = m.CreatedAt,
                        UpdatedAt = m.UpdatedAt,
                        IsDeleted = m.IsDeleted,
                        IsRead = m.IsRead,
                        IsPinned = m.IsPinned,
                        PinnedAt = m.PinnedAt,
                        PinnedBy = m.PinnedBy,
                        Priority = m.Priority,
                        IsMarkedForFollowUp = m.IsMarkedForFollowUp,
                        FollowUpAt = m.FollowUpAt,
                        MarkedForFollowUpBy = m.MarkedForFollowUpBy,
                        IsEdited = m.IsEdited,
                        ReceiverId = m.ReceiverId,
                        SentAt = m.SentAt
                    })
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} messages for group {GroupId}", messages.Count, groupId);
                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/Messages
        [HttpPost]
        public async Task<ActionResult<Message>> SendMessage(Message message)
        {
            try
            {
                // التحقق من صحة البيانات الأساسية
                if (message.GroupId <= 0 || message.SenderId <= 0 || string.IsNullOrEmpty(message.Content))
                {
                    return BadRequest("بيانات الرسالة غير صالحة");
                }

                // Set creation timestamp if not provided
                if (message.CreatedAt == 0)
                {
                    message.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                // إنشاء رسالة جديدة بدون Navigation Properties
                var newMessage = new Message
                {
                    GroupId = message.GroupId,
                    SenderId = message.SenderId,
                    Content = message.Content,
                    ContentType = message.ContentType,
                    ReplyToMessageId = message.ReplyToMessageId,
                    CreatedAt = message.CreatedAt,
                    Priority = message.Priority,
                    ReceiverId = message.ReceiverId,
                    SentAt = message.SentAt
                };

                // استخدام ADO.NET مباشر لتجنب مشكلة shadow properties
                using var connection = _context.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = @"
                    INSERT INTO messages (group_id, sender_id, content, content_type, reply_to_message_id,
                                        created_at, priority, receiver_id, sent_at, updated_at, is_deleted,
                                        is_read, is_pinned, is_marked_for_follow_up, is_edited)
                    VALUES (@groupId, @senderId, @content, @contentType, @replyToMessageId,
                            @createdAt, @priority, @receiverId, @sentAt, @updatedAt, @isDeleted,
                            @isRead, @isPinned, @isMarkedForFollowUp, @isEdited);
                    SELECT CAST(SCOPE_IDENTITY() AS INT);";

                // إضافة المعاملات
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@groupId", newMessage.GroupId));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@senderId", newMessage.SenderId));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@content", newMessage.Content));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@contentType", (int)newMessage.ContentType));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@replyToMessageId", newMessage.ReplyToMessageId ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@createdAt", newMessage.CreatedAt));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@priority", (int)newMessage.Priority));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@receiverId", newMessage.ReceiverId ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@sentAt", newMessage.SentAt ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@updatedAt", newMessage.UpdatedAt ?? (object)DBNull.Value));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isDeleted", false));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isRead", false));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isPinned", false));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isMarkedForFollowUp", false));
                command.Parameters.Add(new Microsoft.Data.SqlClient.SqlParameter("@isEdited", false));

                var result = await command.ExecuteScalarAsync();
                newMessage.Id = Convert.ToInt32(result);

                // Broadcast the message to all clients in the group via SignalR
                await _chatHubContext.Clients.Group(newMessage.GroupId.ToString())
                    .SendAsync("ReceiveMessage", newMessage);

                // إرسال إشعارات للأعضاء في المجموعة
                try
                {
                    await CreateMessageNotifications(newMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطأ في إرسال إشعارات الرسالة الجديدة");
                }

                // تسجيل إرسال الرسالة
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "send_message",
                        "message",
                        newMessage.Id,
                        currentUserId,
                        $"إرسال رسالة في المجموعة #{newMessage.GroupId}");
                }

                return CreatedAtAction(nameof(GetMessage), new { id = newMessage.Id }, newMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message: {Error}", ex.Message);
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/Messages/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateMessage(int id, Message message)
        {
            if (id != message.Id)
            {
                return BadRequest();
            }

            try
            {
                // Set update timestamp
                message.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                
                _context.Entry(message).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                // Broadcast the updated message to all clients in the group via SignalR
                await _chatHubContext.Clients.Group(message.GroupId.ToString())
                    .SendAsync("MessageUpdated", message);

                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!MessageExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating message {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/Messages/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMessage(int id)
        {
            try
            {
                var message = await _context.Messages.FindAsync(id);
                if (message == null)
                {
                    return NotFound();
                }

                // Soft delete
                message.IsDeleted = true;
                message.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                await _context.SaveChangesAsync();

                // Broadcast the deletion to all clients in the group via SignalR
                await _chatHubContext.Clients.Group(message.GroupId.ToString())
                    .SendAsync("MessageDeleted", new { MessageId = id, GroupId = message.GroupId });

                // تسجيل حذف الرسالة
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "delete_message",
                        "message",
                        id,
                        currentUserId,
                        $"حذف رسالة من المجموعة #{message.GroupId}");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting message {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/unread
        [HttpGet("unread")]
        public async Task<ActionResult<IEnumerable<Message>>> GetUnreadMessages([FromQuery] int userId)
        {
            try
            {
                // Get all groups the user is a member of
                var userGroups = await _context.GroupMembers
                    .Where(m => m.UserId == userId)
                    .Select(m => m.GroupId)
                    .ToListAsync();

                if (userGroups.Count == 0)
                {
                    return Ok(new List<Message>());
                }

                // Get all messages in those groups
                var allMessages = await _context.Messages
                    .Where(m => userGroups.Contains(m.GroupId) && !m.IsDeleted)
                    .ToListAsync();

                // Get all messages read by the user
                var readMessageIds = await _context.MessageReads
                    .Where(r => r.UserId == userId)
                    .Select(r => r.MessageId)
                    .ToListAsync();

                // Filter out messages that have been read
                var unreadMessages = allMessages
                    .Where(m => !readMessageIds.Contains(m.Id) && m.SenderId != userId)
                    .OrderByDescending(m => m.CreatedAt)
                    .ToList();

                return Ok(unreadMessages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread messages for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/user/5
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<IEnumerable<Message>>> GetUserMessages(int userId)
        {
            try
            {
                var messages = await _context.Messages
                    .Where(m => m.SenderId == userId && !m.IsDeleted)
                    .OrderByDescending(m => m.CreatedAt)
                    .Take(100) // Limit to prevent large result sets
                    .ToListAsync();

                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/recent?limit=50
        [HttpGet("recent")]
        public async Task<ActionResult<IEnumerable<Message>>> GetRecentMessages([FromQuery] int limit = 50)
        {
            try
            {
                var messages = await _context.Messages
                    .Where(m => !m.IsDeleted)
                    .OrderByDescending(m => m.CreatedAt)
                    .Take(limit)
                    .ToListAsync();

                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent messages");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/Messages/5/mark-read
        [HttpPut("{id}/mark-read")]
        public async Task<IActionResult> MarkMessageAsRead(int id, [FromQuery] int userId)
        {
            try
            {
                var message = await _context.Messages.FindAsync(id);
                if (message == null || message.IsDeleted)
                {
                    return NotFound();
                }

                // التحقق من وجود المستخدم أولاً
                var userExists = await _context.Users.AnyAsync(u => u.Id == userId && !u.IsDeleted);
                if (!userExists)
                {
                    return BadRequest($"User with ID {userId} not found or deleted");
                }

                // Check if already marked as read
                var existingRead = await _context.MessageReads
                    .FirstOrDefaultAsync(r => r.MessageId == id && r.UserId == userId);

                if (existingRead != null)
                {
                    return Ok(new { message = "Message already marked as read" });
                }

                // Mark as read
                var messageRead = new MessageRead
                {
                    MessageId = id,
                    UserId = userId,
                    ReadAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.MessageReads.Add(messageRead);
                await _context.SaveChangesAsync();

                // Notify via SignalR
                await _chatHubContext.Clients.Group(message.GroupId.ToString())
                    .SendAsync("MessageRead", new
                    {
                        MessageId = id,
                        UserId = userId,
                        GroupId = message.GroupId,
                        ReadAt = messageRead.ReadAt
                    });

                // تسجيل قراءة الرسالة
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "read_message",
                        "message",
                        id,
                        currentUserId,
                        $"قراءة رسالة في المجموعة #{message.GroupId}");
                }

                return Ok(new { message = "Message marked as read" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking message {Id} as read", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/Messages/group/5/mark-all-read
        [HttpPut("group/{groupId}/mark-all-read")]
        public async Task<IActionResult> MarkGroupMessagesAsRead(int groupId, [FromQuery] int userId)
        {
            try
            {
                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    return NotFound("Group not found");
                }

                // التحقق من وجود المستخدم أولاً
                var userExists = await _context.Users.AnyAsync(u => u.Id == userId && !u.IsDeleted);
                if (!userExists)
                {
                    return BadRequest($"User with ID {userId} not found or deleted");
                }

                // التحقق من وجود رسائل في المجموعة أولاً
                var messageCount = await _context.Messages
                    .CountAsync(m => m.GroupId == groupId && !m.IsDeleted && m.SenderId != userId);

                if (messageCount == 0)
                {
                    return Ok(new { message = "No messages in this group" });
                }

                // Get all unread messages in the group
                var messages = await _context.Messages
                    .Where(m => m.GroupId == groupId && !m.IsDeleted && m.SenderId != userId)
                    .Select(m => new { m.Id, m.GroupId, m.SenderId })
                    .ToListAsync();

                // Get messages already read by the user
                var readMessageIds = await _context.MessageReads
                    .Where(r => r.UserId == userId)
                    .Select(r => r.MessageId)
                    .ToListAsync();

                // Filter out messages that have already been read
                var unreadMessages = messages
                    .Where(m => !readMessageIds.Contains(m.Id))
                    .ToList();

                if (unreadMessages.Count == 0)
                {
                    return Ok(new { message = "No unread messages found" });
                }

                // Mark all as read
                var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var messageReads = unreadMessages.Select(m => new MessageRead
                {
                    MessageId = m.Id,
                    UserId = userId,
                    ReadAt = now
                }).ToList();

                _context.MessageReads.AddRange(messageReads);
                await _context.SaveChangesAsync();

                // Notify via SignalR
                await _chatHubContext.Clients.Group(groupId.ToString())
                    .SendAsync("GroupMessagesRead", new
                    {
                        GroupId = groupId,
                        UserId = userId,
                        MessageCount = unreadMessages.Count,
                        ReadAt = now
                    });

                return Ok(new { message = $"{unreadMessages.Count} messages marked as read" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all messages as read in group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/search?q=term
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<Message>>> SearchMessages([FromQuery] string q)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                {
                    return BadRequest("Search term is required");
                }

                var messages = await _context.Messages
                    .Where(m => !m.IsDeleted && m.Content.Contains(q))
                    .OrderByDescending(m => m.CreatedAt)
                    .Take(100)
                    .Select(m => new {
                        id = m.Id,
                        groupId = m.GroupId  ,
                        senderId = m.SenderId,
                        content = m.Content,
                        contentType = m.ContentType,
                        replyToMessageId = m.ReplyToMessageId,
                        createdAt = m.CreatedAt,
                        updatedAt = m.UpdatedAt,
                        isDeleted = m.IsDeleted,
                        isRead = m.IsRead,
                        isPinned = m.IsPinned,
                        pinnedAt = m.PinnedAt,
                        pinnedBy = m.PinnedBy,
                        priority = m.Priority,
                        isMarkedForFollowUp = m.IsMarkedForFollowUp,
                        followUpAt = m.FollowUpAt,
                        markedForFollowUpBy = m.MarkedForFollowUpBy,
                        isEdited = m.IsEdited,
                        receiverId = m.ReceiverId,
                        sentAt = m.SentAt,
                        mentionedUserIds = m.MentionedUserIds,
                        senderName = "مجهول" // سنحصل على الاسم لاحقاً
                    })
                    .ToListAsync();

                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching messages with term {SearchTerm}", q);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/group/5/search?q=term
        [HttpGet("group/{groupId}/search")]
        public async Task<ActionResult<IEnumerable<Message>>> SearchGroupMessages(int groupId, [FromQuery] string q)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                {
                    return BadRequest("Search term is required");
                }

                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    return NotFound("Group not found");
                }

                var messages = await _context.Messages
                    .Where(m => m.GroupId == groupId && !m.IsDeleted && m.Content.Contains(q))
                    .OrderByDescending(m => m.CreatedAt)
                    .ToListAsync();

                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching messages in group {GroupId} with term {SearchTerm}", groupId, q);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/group/5/date-range?start=1609459200&end=1612137600
        [HttpGet("group/{groupId}/date-range")]
        public async Task<ActionResult<IEnumerable<Message>>> GetMessagesByDateRange(
            int groupId, [FromQuery] long start, [FromQuery] long end)
        {
            try
            {
                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    return NotFound("Group not found");
                }

                var messages = await _context.Messages
                    .Where(m => m.GroupId == groupId && !m.IsDeleted && 
                           m.CreatedAt >= start && m.CreatedAt <= end)
                    .OrderBy(m => m.CreatedAt)
                    .ToListAsync();

                return Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages by date range for group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/unread-count
        [HttpGet("unread-count")]
        public async Task<ActionResult<object>> GetUnreadMessageCount([FromQuery] int userId)
        {
            try
            {
                // Get all groups the user is a member of
                var userGroups = await _context.GroupMembers
                    .Where(m => m.UserId == userId)
                    .Select(m => m.GroupId)
                    .ToListAsync();

                if (userGroups.Count == 0)
                {
                    return Ok(new { count = 0 });
                }

                // Get count of all messages in those groups
                var allMessagesCount = await _context.Messages
                    .CountAsync(m => userGroups.Contains(m.GroupId) && !m.IsDeleted && m.SenderId != userId);

                // Get count of all messages read by the user
                var readMessagesCount = await _context.MessageReads
                    .CountAsync(r => r.UserId == userId);

                // Calculate unread count
                var unreadCount = allMessagesCount - readMessagesCount;
                unreadCount = unreadCount < 0 ? 0 : unreadCount;

                return Ok(new { count = unreadCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread message count for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/group/5/unread-count
        [HttpGet("group/{groupId}/unread-count")]
        public async Task<ActionResult<object>> GetGroupUnreadMessageCount(int groupId, [FromQuery] int userId)
        {
            try
            {
                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    return NotFound("Group not found");
                }

                // Get count of all messages in the group
                var allMessagesCount = await _context.Messages
                    .CountAsync(m => m.GroupId == groupId && !m.IsDeleted && m.SenderId != userId);

                // Get IDs of all messages in the group
                var messageIds = await _context.Messages
                    .Where(m => m.GroupId == groupId && !m.IsDeleted && m.SenderId != userId)
                    .Select(m => m.Id)
                    .ToListAsync();

                // Get count of messages read by the user
                var readMessagesCount = await _context.MessageReads
                    .CountAsync(r => r.UserId == userId && messageIds.Contains(r.MessageId));

                // Calculate unread count
                var unreadCount = allMessagesCount - readMessagesCount;
                unreadCount = unreadCount < 0 ? 0 : unreadCount;

                return Ok(new { count = unreadCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread message count for group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        // PATCH: api/Messages/5/pin/3
        [HttpPatch("{id}/pin/{userId}")]
        public async Task<IActionResult> PinMessage(int id, int userId)
        {
            try
            {
                var message = await _context.Messages.FindAsync(id);
                if (message == null || message.IsDeleted)
                {
                    return NotFound();
                }

                message.IsPinned = true;
                message.PinnedBy = userId;
                message.PinnedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                message.UpdatedAt = message.PinnedAt;

                await _context.SaveChangesAsync();

                // Notify via SignalR
                await _chatHubContext.Clients.Group(message.GroupId.ToString())
                    .SendAsync("MessagePinned", message);

                return Ok(new { message = "Message pinned successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error pinning message {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/Messages/5/reply
        [HttpPost("{messageId}/reply")]
        public async Task<ActionResult<Message>> ReplyToMessage(int messageId, Message reply)
        {
            try
            {
                var originalMessage = await _context.Messages.FindAsync(messageId);
                if (originalMessage == null || originalMessage.IsDeleted)
                {
                    return NotFound("Original message not found");
                }

                // Set reply properties
                reply.ReplyToMessageId = messageId;
                
                // Set creation timestamp if not provided
                if (reply.CreatedAt == 0)
                {
                    reply.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                _context.Messages.Add(reply);
                await _context.SaveChangesAsync();

                // Broadcast the reply to all clients in the group via SignalR
                await _chatHubContext.Clients.Group(reply.GroupId.ToString())
                    .SendAsync("ReceiveMessage", reply);

                return CreatedAtAction(nameof(GetMessage), new { id = reply.Id }, reply);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error replying to message {MessageId}", messageId);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/Messages/5/forward
        [HttpPost("{messageId}/forward")]
        public async Task<ActionResult<Message>> ForwardMessage(int messageId, [FromBody] ForwardMessageRequest request)
        {
            try
            {
                var originalMessage = await _context.Messages.FindAsync(messageId);
                if (originalMessage == null || originalMessage.IsDeleted)
                {
                    return NotFound("Original message not found");
                }

                var targetGroup = await _context.ChatGroups.FindAsync(request.ToGroupId);
                if (targetGroup == null)
                {
                    return NotFound("Target group not found");
                }

                // Create forwarded message
                var forwardedMessage = new Message
                {
                    Content = originalMessage.Content,
                    SenderId = originalMessage.SenderId, // Keep original sender
                    GroupId = request.ToGroupId,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    MessageType = originalMessage.MessageType,
                    // Add forwarding metadata
                    ReplyToMessageId = null // Not a reply
                };

                _context.Messages.Add(forwardedMessage);
                await _context.SaveChangesAsync();

                // Broadcast the forwarded message to all clients in the target group via SignalR
                await _chatHubContext.Clients.Group(request.ToGroupId.ToString())
                    .SendAsync("ReceiveMessage", forwardedMessage);

                return CreatedAtAction(nameof(GetMessage), new { id = forwardedMessage.Id }, forwardedMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error forwarding message {MessageId}", messageId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/Messages/statistics
        [HttpGet("statistics")]
        public async Task<ActionResult<object>> GetMessageStatistics()
        {
            try
            {
                var totalMessages = await _context.Messages.CountAsync(m => !m.IsDeleted);
                var totalGroups = await _context.ChatGroups.CountAsync(g => !g.IsArchived);
                var activeGroups = await _context.Messages
                    .Where(m => !m.IsDeleted && m.CreatedAt >= DateTimeOffset.UtcNow.AddDays(-7).ToUnixTimeSeconds())
                    .Select(m => m.GroupId)
                    .Distinct()
                    .CountAsync();

                var messagesLast24Hours = await _context.Messages
                    .CountAsync(m => !m.IsDeleted && m.CreatedAt >= DateTimeOffset.UtcNow.AddDays(-1).ToUnixTimeSeconds());

                var messagesLast7Days = await _context.Messages
                    .CountAsync(m => !m.IsDeleted && m.CreatedAt >= DateTimeOffset.UtcNow.AddDays(-7).ToUnixTimeSeconds());

                var messagesLast30Days = await _context.Messages
                    .CountAsync(m => !m.IsDeleted && m.CreatedAt >= DateTimeOffset.UtcNow.AddDays(-30).ToUnixTimeSeconds());

                return Ok(new
                {
                    totalMessages,
                    totalGroups,
                    activeGroups,
                    messagesLast24Hours,
                    messagesLast7Days,
                    messagesLast30Days
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting message statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/Messages/group/5
        [HttpDelete("group/{groupId}")]
        public async Task<IActionResult> DeleteGroupMessages(int groupId)
        {
            try
            {
                var group = await _context.ChatGroups.FindAsync(groupId);
                if (group == null)
                {
                    return NotFound("Group not found");
                }

                // Soft delete all messages in the group
                var messages = await _context.Messages
                    .Where(m => m.GroupId == groupId && !m.IsDeleted)
                    .ToListAsync();

                foreach (var message in messages)
                {
                    message.IsDeleted = true;
                    message.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                await _context.SaveChangesAsync();

                // Notify via SignalR
                await _chatHubContext.Clients.Group(groupId.ToString())
                    .SendAsync("GroupMessagesDeleted", new { GroupId = groupId });

                return Ok(new { message = $"{messages.Count} messages deleted" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting messages for group {GroupId}", groupId);
                return StatusCode(500, "Internal server error");
            }
        }

        private bool MessageExists(int id)
        {
            return _context.Messages.Any(e => e.Id == id);
        }

        /// <summary>
        /// إرسال إشعارات للأعضاء في المجموعة عند إرسال رسالة جديدة
        /// </summary>
        private async System.Threading.Tasks.Task CreateMessageNotifications(Message message)
        {
            try
            {
                // الحصول على أعضاء المجموعة (باستثناء المرسل)
                var groupMembers = await _context.GroupMembers
                    .Where(gm => gm.GroupId == message.GroupId &&
                                gm.UserId != message.SenderId &&
                                !gm.IsDeleted)
                    .Select(gm => gm.UserId)
                    .ToListAsync();

                if (groupMembers.Any())
                {
                    // الحصول على اسم المجموعة
                    var group = await _context.ChatGroups
                        .FirstOrDefaultAsync(g => g.Id == message.GroupId);

                    var groupName = group?.Name ?? "مجموعة";

                    // الحصول على اسم المرسل
                    var sender = await _context.Users
                        .FirstOrDefaultAsync(u => u.Id == message.SenderId);

                    var senderName = sender?.Name ?? "مستخدم";

                    // إرسال إشعارات للأعضاء
                    await _notificationService.CreateAndSendNotificationsAsync(
                        groupMembers,
                        "رسالة جديدة في المجموعة",
                        $"رسالة جديدة من {senderName} في مجموعة '{groupName}': {(message.Content?.Length > 50 ? message.Content.Substring(0, 50) + "..." : message.Content)}",
                        "message_received",
                        message.GroupId
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال إشعارات الرسالة الجديدة للمجموعة {GroupId}", message.GroupId);
            }
        }

        /// <summary>
        /// البحث في مرفقات الرسائل - للبحث الموحد
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="userId">معرف المستخدم للتحقق من الصلاحيات (اختياري)</param>
        /// <param name="limit">عدد النتائج المطلوبة (افتراضي: 50)</param>
        /// <returns>قائمة مرفقات الرسائل المطابقة</returns>
        /// <response code="200">إرجاع قائمة مرفقات الرسائل المطابقة</response>
        [HttpGet("attachments/search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> SearchMessageAttachments(
            [FromQuery] string searchTerm,
            [FromQuery] int? userId = null,
            [FromQuery] int limit = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return Ok(new List<object>());
                }

                var currentUserId = GetCurrentUserId();
                var targetUserId = userId ?? currentUserId;

                // البحث في مرفقات الرسائل مع التحقق من صلاحيات الوصول للمجموعات
                var query = _context.MessageAttachments
                    .Include(ma => ma.Message)
                    .ThenInclude(m => m.Group)
                    .Include(ma => ma.UploadedByNavigation)
                    .Where(ma => !ma.IsDeleted);

                // تطبيق فلتر الصلاحيات - المرفقات للرسائل في المجموعات التي يمكن للمستخدم الوصول إليها
                if (targetUserId > 0)
                {
                    query = query.Where(ma =>
                        ma.Message != null && ma.Message.Group != null && (
                            // المجموعات التي هو عضو فيها
                            _context.GroupMembers.Any(cgm =>
                                cgm.GroupId == ma.Message.GroupId &&
                                cgm.UserId == targetUserId &&
                                !cgm.IsDeleted
                            )
                        )
                    );
                }

                // البحث في أسماء وأنواع مرفقات الرسائل
                query = query.Where(ma =>
                    // البحث في اسم الملف
                    ma.FileName.Contains(searchTerm) ||
                    // البحث في نوع الملف
                    ma.FileType.Contains(searchTerm) ||
                    // البحث في مسار الملف
                    (ma.FilePath != null && ma.FilePath.Contains(searchTerm))
                );

                // تطبيق الحد الأقصى للنتائج والترتيب
                var attachments = await query
                    .OrderByDescending(ma => ma.UploadedAt)
                    .Take(Math.Min(limit, 100)) // حد أقصى 100 نتيجة
                    .Select(ma => new {
                        ma.Id,
                        ma.FileName,
                        ma.FileType,
                        ma.FileSize,
                        ma.FilePath,
                        ma.UploadedAt,
                        ma.MessageId,
                        GroupName = ma.Message != null && ma.Message.Group != null ? ma.Message.Group.Name : null,
                        UploaderName = ma.UploadedByNavigation != null ? ma.UploadedByNavigation.Name : null
                    })
                    .ToListAsync();

                _logger.LogInformation("البحث في مرفقات الرسائل: تم العثور على {Count} نتيجة للمصطلح '{SearchTerm}'",
                    attachments.Count, searchTerm);

                return Ok(attachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث في مرفقات الرسائل بالمصطلح: {SearchTerm}", searchTerm);
                return StatusCode(500, "خطأ في الخادم أثناء البحث");
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي من JWT token
        /// </summary>
        private int GetCurrentUserId()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value ??
                                 User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }
    }

    public class ForwardMessageRequest
    {
        public int ToGroupId { get; set; }
    }
}