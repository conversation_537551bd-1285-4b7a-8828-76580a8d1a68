import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/search_models.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../../../controllers/unified_search_controller.dart';
import '../../../constants/app_styles.dart';
import '../../../routes/app_routes.dart';

/// مكون البحث العام
/// يمكن استخدامه في أي مكان في التطبيق للوصول إلى البحث الشامل
class GlobalSearchWidget extends StatefulWidget {
  /// عرض المكون
  final double? width;

  /// ارتفاع المكون
  final double height;

  /// نص التلميح
  final String hintText;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون الحدود
  final Color? borderColor;

  /// نصف قطر الحدود
  final double borderRadius;

  /// سماكة الحدود
  final double borderWidth;

  /// التباعد الداخلي
  final EdgeInsetsGeometry padding;

  /// أنواع النتائج المطلوبة
  final List<SearchResultType>? searchTypes;

  /// عدد النتائج المعروضة
  final int maxResults;

  /// دالة يتم استدعاؤها عند النقر على نتيجة
  final Function(SearchResult)? onResultTap;

  /// إنشاء مكون البحث العام
  const GlobalSearchWidget({
    super.key,
    this.width,
    this.height = 50,
    this.hintText = 'ابحث في النظام...',
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 8.0,
    this.borderWidth = 1.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
    this.searchTypes,
    this.maxResults = 5,
    this.onResultTap,
  });

  @override
  State<GlobalSearchWidget> createState() => _GlobalSearchWidgetState();
}

class _GlobalSearchWidgetState extends State<GlobalSearchWidget> {
  // وحدة تحكم البحث
  final UnifiedSearchController _searchController =
      Get.find<UnifiedSearchController>();

  // تحكم النص
  final TextEditingController _textController = TextEditingController();

  // مؤشر التركيز
  final FocusNode _focusNode = FocusNode();

  // حالة عرض نتائج البحث
  final RxBool _showResults = false.obs;

  // مؤقت البحث
  Timer? _searchTimer;

  @override
  void initState() {
    super.initState();

    // الاستماع لتغييرات التركيز
    _focusNode.addListener(() {
      _showResults.value =
          _focusNode.hasFocus && _textController.text.isNotEmpty;
    });

    // تعيين أنواع البحث
    if (widget.searchTypes != null) {
      _searchController.selectedTypes.assignAll(widget.searchTypes!);
    }

    // تعيين عدد النتائج
    _searchController.resultsPerType.value = widget.maxResults;
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // حقل البحث
        Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? Colors.grey.shade100,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border: Border.all(
              color: widget.borderColor ?? Colors.grey.shade300,
              width: widget.borderWidth,
            ),
          ),
          child: TextField(
            controller: _textController,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _textController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _textController.clear();
                        _searchController.clearSearch();
                        _showResults.value = false;
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: widget.padding,
            ),
            onChanged: (value) {
              // إلغاء المؤقت السابق
              _searchTimer?.cancel();

              if (value.isEmpty) {
                _searchController.clearSearch();
                _showResults.value = false;
                return;
              }

              // تعيين مؤقت جديد للبحث بعد توقف المستخدم عن الكتابة
              _searchTimer = Timer(const Duration(milliseconds: 300), () {
                _searchController.search(value);
                _showResults.value = true;
              });
            },
            onSubmitted: (value) {
              if (value.isNotEmpty) {
                // الانتقال إلى شاشة البحث الشامل
                Get.toNamed(AppRoutes.unifiedSearch, arguments: value);
                _showResults.value = false;
              }
            },
          ),
        ),

        // نتائج البحث
        Obx(() {
          if (!_showResults.value) {
            return const SizedBox.shrink();
          }

          return _buildSearchResults();
        }),
      ],
    );
  }

  /// بناء نتائج البحث
  Widget _buildSearchResults() {
    return Obx(() {
      if (_searchController.isSearching.value) {
        return Container(
          width: widget.width,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Center(
            child: SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        );
      }

      if (_searchController.searchResults.isEmpty) {
        return Container(
          width: widget.width,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Center(
            child: Text(
              'لا توجد نتائج لـ "${_textController.text}"',
              style: AppStyles.bodyMedium,
            ),
          ),
        );
      }

      return Container(
        width: widget.width,
        constraints: BoxConstraints(
          maxHeight: 400,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // نتائج البحث
            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _searchController.searchResults.length,
                itemBuilder: (context, index) {
                  final result = _searchController.searchResults[index];
                  return ListTile(
                    leading: _getIconForType(result.type),
                    title: Text(
                      result.title,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    subtitle: Text(
                      result.subtitle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    onTap: () {
                      // إغلاق نتائج البحث
                      _showResults.value = false;

                      // استدعاء دالة النقر على النتيجة
                      if (widget.onResultTap != null) {
                        widget.onResultTap!(result);
                      } else {
                        _navigateToResult(result);
                      }
                    },
                  );
                },
              ),
            ),

            // زر عرض المزيد من النتائج
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextButton(
                onPressed: () {
                  // الانتقال إلى شاشة البحث الشامل
                  Get.toNamed(AppRoutes.unifiedSearch,
                      arguments: _textController.text);
                  _showResults.value = false;
                },
                child: const Text('عرض المزيد من النتائج'),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// الحصول على أيقونة لنوع النتيجة
  Widget _getIconForType(SearchResultType type) {
    switch (type) {
      case SearchResultType.task:
        return const CircleAvatar(
          backgroundColor: Colors.blue,
          radius: 16,
          child: Icon(Icons.task_alt, color: Colors.white, size: 16),
        );
      case SearchResultType.message:
        return const CircleAvatar(
          backgroundColor: Colors.green,
          radius: 16,
          child: Icon(Icons.message, color: Colors.white, size: 16),
        );
      case SearchResultType.user:
        return const CircleAvatar(
          backgroundColor: Colors.orange,
          radius: 16,
          child: Icon(Icons.person, color: Colors.white, size: 16),
        );
      case SearchResultType.document:
        return const CircleAvatar(
          backgroundColor: Colors.purple,
          radius: 16,
          child: Icon(Icons.description, color: Colors.white, size: 16),
        );
      case SearchResultType.event:
        return const CircleAvatar(
          backgroundColor: Colors.red,
          radius: 16,
          child: Icon(Icons.event, color: Colors.white, size: 16),
        );
      case SearchResultType.report:
        return const CircleAvatar(
          backgroundColor: Colors.teal,
          radius: 16,
          child: Icon(Icons.bar_chart, color: Colors.white, size: 16),
        );
      case SearchResultType.attachment:
        return const CircleAvatar(
          backgroundColor: Colors.indigo,
          radius: 16,
          child: Icon(Icons.attach_file, color: Colors.white, size: 16),
        );
      case SearchResultType.messageAttachment:
        return const CircleAvatar(
          backgroundColor: Colors.deepPurple,
          radius: 16,
          child: Icon(Icons.attach_email, color: Colors.white, size: 16),
        );
      case SearchResultType.comment:
        return const CircleAvatar(
          backgroundColor: Colors.brown,
          radius: 16,
          child: Icon(Icons.comment, color: Colors.white, size: 16),
        );
      case SearchResultType.department:
        return const CircleAvatar(
          backgroundColor: Colors.cyan,
          radius: 16,
          child: Icon(Icons.business, color: Colors.white, size: 16),
        );
      case SearchResultType.role:
        return const CircleAvatar(
          backgroundColor: Colors.amber,
          radius: 16,
          child: Icon(Icons.admin_panel_settings, color: Colors.white, size: 16),
        );
      case SearchResultType.other:
        return const CircleAvatar(
          backgroundColor: Colors.grey,
          radius: 16,
          child: Icon(Icons.more_horiz, color: Colors.white, size: 16),
        );
    }
  }

  /// الانتقال إلى نتيجة البحث
  void _navigateToResult(SearchResult result) {
    switch (result.type) {
      case SearchResultType.task:
        // الانتقال إلى صفحة المهمة
        Get.toNamed(AppRoutes.taskDetail,
            arguments: {'taskId': result.data.id});
        break;
      case SearchResultType.message:
        // الانتقال إلى صفحة المحادثة
        Get.toNamed(AppRoutes.unifiedChatDetail, arguments: {
          'chatGroup': {'id': result.data.groupId},
          'messageId': result.id,
        });
        break;
      case SearchResultType.user:
        // الانتقال إلى صفحة المستخدم
        Get.toNamed(AppRoutes.userDashboard, arguments: result.data);
        break;
      case SearchResultType.document:
        // الانتقال إلى صفحة الوثيقة
        Get.toNamed(AppRoutes.documentBrowser);
        break;
      case SearchResultType.event:
        // الانتقال إلى صفحة الحدث
        Get.toNamed(AppRoutes.calendarEventDetails, arguments: {
          'event': result.data,
          'onEventUpdated': null,
          'onEventDeleted': null,
        });
        break;
      case SearchResultType.report:
        // الانتقال إلى صفحة التقرير
        Get.toNamed(AppRoutes.reportDetails,
            arguments: {'reportId': result.data.id});
        break;
      case SearchResultType.attachment:
        // الانتقال إلى صفحة المهمة التي تحتوي على المرفق
        if (result.data != null && result.data['TaskId'] != null) {
          Get.toNamed(AppRoutes.taskDetail,
              arguments: {'taskId': result.data['TaskId']});
        }
        break;
      case SearchResultType.messageAttachment:
        // عرض معلومات مرفق الرسالة
        Get.snackbar('مرفق رسالة', 'تم العثور على مرفق في الرسائل');
        break;
      case SearchResultType.comment:
        // الانتقال إلى صفحة المهمة التي تحتوي على التعليق
        if (result.data != null && result.data['TaskId'] != null) {
          Get.toNamed(AppRoutes.taskDetail,
              arguments: {'taskId': result.data['TaskId']});
        }
        break;
      case SearchResultType.department:
        // عرض معلومات القسم
        Get.snackbar('قسم', 'تم العثور على قسم: ${result.title}');
        break;
      case SearchResultType.role:
        // عرض معلومات الدور
        Get.snackbar('دور', 'تم العثور على دور: ${result.title}');
        break;
      case SearchResultType.other:
        // لا يوجد إجراء محدد
        break;
    }
  }
}
