using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.SignalR;
using webApi.Models;
using webApi.Models.DTOs;
using webApi.Hubs;
using webApi.Services;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة تعليقات المهام
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class TaskCommentsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly IHubContext<TaskCommentsHub> _commentsHub;
        private readonly INotificationService _notificationService;
        private readonly ILoggingService _loggingService;
        private readonly ILogger<TaskCommentsController> _logger;

        public TaskCommentsController(
            TasksDbContext context,
            IHubContext<TaskCommentsHub> commentsHub,
            INotificationService notificationService,
            ILoggingService loggingService,
            ILogger<TaskCommentsController> logger)
        {
            _context = context;
            _commentsHub = commentsHub;
            _notificationService = notificationService;
            _loggingService = loggingService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع تعليقات المهام
        /// </summary>
        /// <returns>قائمة بجميع التعليقات</returns>
        /// <response code="200">إرجاع قائمة التعليقات</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskComment>>> GetTaskComments()
        {
            return await _context.TaskComments
                .Include(tc => tc.User)
                .Include(tc => tc.Task)
                
                .Where(tc => !tc.IsDeleted)
                .OrderByDescending(tc => tc.Id)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على تعليق محدد
        /// </summary>
        /// <param name="id">معرف التعليق</param>
        /// <returns>التعليق المطلوب</returns>
        /// <response code="200">إرجاع التعليق</response>
        /// <response code="404">التعليق غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TaskComment>> GetTaskComment(int id)
        {
            var taskComment = await _context.TaskComments
                .Include(tc => tc.User)
                .Include(tc => tc.Task)
                .FirstOrDefaultAsync(tc => tc.Id == id && !tc.IsDeleted);

            if (taskComment == null)
            {
                return NotFound();
            }

            return taskComment;
        }

        /// <summary>
        /// الحصول على تعليقات مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>قائمة تعليقات المهمة</returns>
        /// <response code="200">إرجاع قائمة التعليقات</response>
        [HttpGet("task/{taskId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskComment>>> GetTaskCommentsByTask(int taskId)
        {
            return await _context.TaskComments
                .Include(tc => tc.User)
                .Where(tc => tc.TaskId == taskId && !tc.IsDeleted)
                .OrderBy(tc => tc.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على تعليقات مستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة تعليقات المستخدم</returns>
        /// <response code="200">إرجاع قائمة التعليقات</response>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskComment>>> GetTaskCommentsByUser(int userId)
        {
            return await _context.TaskComments
                .Include(tc => tc.Task)
                .Where(tc => tc.UserId == userId && !tc.IsDeleted)
                .OrderByDescending(tc => tc.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// عدد تعليقات مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>عدد التعليقات</returns>
        /// <response code="200">إرجاع العدد</response>
        [HttpGet("task/{taskId}/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetTaskCommentsCount(int taskId)
        {
            var count = await _context.TaskComments
                .CountAsync(tc => tc.TaskId == taskId && !tc.IsDeleted);

            return count;
        }

        /// <summary>
        /// إنشاء تعليق جديد - نسخة مبسطة
        /// </summary>
        /// <param name="createDto">بيانات التعليق</param>
        /// <returns>التعليق المُنشأ</returns>
        /// <response code="201">تم إنشاء التعليق بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost("simple")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TaskComment>> PostTaskCommentSimple([FromBody] CreateTaskCommentDto createDto)
        {
            try
            {
                // التحقق من صحة النموذج
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // التحقق من وجود المهمة
                var taskExists = await _context.Tasks.AnyAsync(t => t.Id == createDto.TaskId);
                if (!taskExists)
                {
                    return BadRequest("المهمة غير موجودة");
                }

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == createDto.UserId);
                if (!userExists)
                {
                    return BadRequest("المستخدم غير موجود");
                }

                // إنشاء كائن التعليق
                var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var taskComment = new TaskComment
                {
                    TaskId = createDto.TaskId,
                    UserId = createDto.UserId,
                    Content = createDto.Content.Trim(),
                    CreatedAt = now,
                    UpdatedAt = now,
                    IsDeleted = false
                };

                // إضافة التعليق
                _context.TaskComments.Add(taskComment);

                // إضافة سجل TaskHistory لإضافة التعليق
                var taskHistory = new TaskHistory
                {
                    TaskId = createDto.TaskId,
                    UserId = createDto.UserId,
                    Action = "إضافة_تعليق",
                    Details = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        commentId = taskComment.Id,
                        contentPreview = createDto.Content.Length > 100 ?
                            createDto.Content.Substring(0, 100) + "..." :
                            createDto.Content
                    }),
                    Timestamp = now,
                    ChangeType = "تعليق",
                    ChangeDescription = "تمت إضافة تعليق جديد",
                    OldValue = "",
                    NewValue = createDto.Content.Length > 100 ?
                        createDto.Content.Substring(0, 100) + "..." :
                        createDto.Content,
                    ChangedBy = createDto.UserId,
                    ChangedAt = now
                };

                _context.TaskHistories.Add(taskHistory);
                await _context.SaveChangesAsync();

                // تحميل بيانات المستخدم والمهمة (بدون تضمين taskComments لتجنب المراجع الدائرية)
                var savedComment = await _context.TaskComments
                    .Include(tc => tc.User)
                    .Include(tc => tc.Task)
                    .FirstOrDefaultAsync(tc => tc.Id == taskComment.Id);

                // إزالة المراجع الدائرية
                if (savedComment?.Task != null)
                {
                    savedComment.Task.TaskComments = new List<TaskComment>();
                }

                return CreatedAtAction("GetTaskComment", new { id = taskComment.Id }, savedComment);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التعليق: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return StatusCode(500, $"حدث خطأ في الخادم: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء تعليق جديد - النسخة الأصلية
        /// </summary>
        /// <param name="createDto">بيانات التعليق</param>
        /// <returns>التعليق المُنشأ</returns>
        /// <response code="201">تم إنشاء التعليق بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TaskComment>> PostTaskComment(CreateTaskCommentDto createDto)
        {
            try
            {
                // التحقق من صحة النموذج
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // التحقق من وجود المهمة
                var taskExists = await _context.Tasks.AnyAsync(t => t.Id == createDto.TaskId);
                if (!taskExists)
                {
                    return BadRequest("المهمة غير موجودة");
                }

                // التحقق من وجود المستخدم
                var userExists = await _context.Users.AnyAsync(u => u.Id == createDto.UserId);
                if (!userExists)
                {
                    return BadRequest("المستخدم غير موجود");
                }

                // إنشاء كائن التعليق
                var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var taskComment = new TaskComment
                {
                    TaskId = createDto.TaskId,
                    UserId = createDto.UserId,
                    Content = createDto.Content.Trim(),
                    CreatedAt = now,
                    UpdatedAt = now,
                    IsDeleted = false
                };

                // إضافة التعليق
                _context.TaskComments.Add(taskComment);
                await _context.SaveChangesAsync();

                // تحميل بيانات المستخدم والمهمة (بدون تضمين taskComments لتجنب المراجع الدائرية)
                var savedComment = await _context.TaskComments
                    .Include(tc => tc.User)
                    .Include(tc => tc.Task)
                    .FirstOrDefaultAsync(tc => tc.Id == taskComment.Id);

                // إزالة المراجع الدائرية
                if (savedComment?.Task != null)
                {
                    savedComment.Task.TaskComments = new List<TaskComment>();
                }

                // إرسال إشعارات للمستخدمين المعنيين
                await CreateCommentNotifications(savedComment);

                // بث التعليق الجديد عبر SignalR
                try
                {
                    await _commentsHub.Clients.Group($"TaskComments_{taskComment.TaskId}")
                        .SendAsync("ReceiveTaskComment", savedComment);
                }
                catch (Exception signalREx)
                {
                    // لا نريد أن يفشل إنشاء التعليق بسبب SignalR
                    Console.WriteLine($"خطأ في SignalR: {signalREx.Message}");
                }

                // تسجيل إضافة التعليق
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "add_comment",
                        "task",
                        createDto.TaskId,
                        currentUserId,
                        $"إضافة تعليق على المهمة #{createDto.TaskId}");
                }

                return CreatedAtAction("GetTaskComment", new { id = taskComment.Id }, savedComment);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التعليق: {ex.Message}");
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }

        /// <summary>
        /// تحديث تعليق
        /// </summary>
        /// <param name="id">معرف التعليق</param>
        /// <param name="taskComment">بيانات التعليق المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث التعليق بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">التعليق غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutTaskComment(int id, TaskComment taskComment)
        {
            if (id != taskComment.Id)
            {
                return BadRequest();
            }

            // الحصول على التعليق الأصلي لمقارنة التغييرات
            var originalComment = await _context.TaskComments
                .AsNoTracking()
                .FirstOrDefaultAsync(tc => tc.Id == id);

            if (originalComment == null)
            {
                return NotFound();
            }

            var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            taskComment.UpdatedAt = now;
            _context.Entry(taskComment).State = EntityState.Modified;

            // إضافة سجل TaskHistory لتعديل التعليق
            var taskHistory = new TaskHistory
            {
                TaskId = taskComment.TaskId,
                UserId = taskComment.UserId,
                Action = "تعديل_تعليق",
                Details = System.Text.Json.JsonSerializer.Serialize(new
                {
                    commentId = taskComment.Id,
                    oldContentPreview = originalComment.Content.Length > 100 ?
                        originalComment.Content.Substring(0, 100) + "..." :
                        originalComment.Content,
                    newContentPreview = taskComment.Content.Length > 100 ?
                        taskComment.Content.Substring(0, 100) + "..." :
                        taskComment.Content
                }),
                Timestamp = now,
                ChangeType = "تعليق",
                ChangeDescription = "تم تعديل التعليق",
                OldValue = originalComment.Content.Length > 100 ?
                    originalComment.Content.Substring(0, 100) + "..." :
                    originalComment.Content,
                NewValue = taskComment.Content.Length > 100 ?
                    taskComment.Content.Substring(0, 100) + "..." :
                    taskComment.Content,
                ChangedBy = taskComment.UserId,
                ChangedAt = now
            };

            _context.TaskHistories.Add(taskHistory);

            try
            {
                await _context.SaveChangesAsync();
                // بث التحديث عبر SignalR
                await _commentsHub.Clients.Group($"TaskComments_{taskComment.TaskId}").SendAsync("TaskCommentUpdated", taskComment);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TaskCommentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// حذف تعليق (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف التعليق</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف التعليق بنجاح</response>
        /// <response code="404">التعليق غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTaskComment(int id)
        {
            var taskComment = await _context.TaskComments.FindAsync(id);
            if (taskComment == null || taskComment.IsDeleted)
            {
                return NotFound();
            }

            // حذف منطقي
            var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            taskComment.IsDeleted = true;
            taskComment.UpdatedAt = now;

            // إضافة سجل TaskHistory لحذف التعليق
            var currentUserId = GetCurrentUserId();
            var taskHistory = new TaskHistory
            {
                TaskId = taskComment.TaskId,
                UserId = currentUserId > 0 ? currentUserId : taskComment.UserId,
                Action = "حذف_تعليق",
                Details = System.Text.Json.JsonSerializer.Serialize(new
                {
                    commentId = taskComment.Id,
                    deletedContentPreview = taskComment.Content.Length > 100 ?
                        taskComment.Content.Substring(0, 100) + "..." :
                        taskComment.Content
                }),
                Timestamp = now,
                ChangeType = "تعليق",
                ChangeDescription = "تم حذف التعليق",
                OldValue = taskComment.Content.Length > 100 ?
                    taskComment.Content.Substring(0, 100) + "..." :
                    taskComment.Content,
                NewValue = "",
                ChangedBy = currentUserId > 0 ? currentUserId : taskComment.UserId,
                ChangedAt = now
            };

            _context.TaskHistories.Add(taskHistory);
            await _context.SaveChangesAsync();

            // بث الحذف عبر SignalR
            await _commentsHub.Clients.Group($"TaskComments_{taskComment.TaskId}").SendAsync("TaskCommentDeleted", new { CommentId = id });

            return NoContent();
        }

        /// <summary>
        /// حذف تعليق نهائياً
        /// </summary>
        /// <param name="id">معرف التعليق</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف التعليق نهائياً</response>
        /// <response code="404">التعليق غير موجود</response>
        [HttpDelete("{id}/permanent")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTaskCommentPermanent(int id)
        {
            var taskComment = await _context.TaskComments.FindAsync(id);
            if (taskComment == null)
            {
                return NotFound();
            }

            _context.TaskComments.Remove(taskComment);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool TaskCommentExists(int id)
        {
            return _context.TaskComments.Any(e => e.Id == id && !e.IsDeleted);
        }

        /// <summary>
        /// إنشاء إشعارات للمستخدمين المعنيين بالتعليق
        /// </summary>
        private async System.Threading.Tasks.Task CreateCommentNotifications(TaskComment comment)
        {
            try
            {
                if (comment?.Task == null) return;

                // الحصول على المستخدمين المعنيين بالمهمة
                var userIds = new List<int>();

                // إضافة منشئ المهمة
                if (comment.Task.CreatorId != comment.UserId)
                {
                    userIds.Add(comment.Task.CreatorId);
                }

                // إضافة المسند له
                if (comment.Task.AssigneeId.HasValue &&
                    comment.Task.AssigneeId.Value != comment.UserId &&
                    !userIds.Contains(comment.Task.AssigneeId.Value))
                {
                    userIds.Add(comment.Task.AssigneeId.Value);
                }

                // إضافة المستخدمين الذين لهم وصول للمهمة
                var accessUsers = await _context.TaskAccessUsers
                    .Where(au => au.TaskId == comment.TaskId &&
                               au.UserId != comment.UserId &&
                               !userIds.Contains(au.UserId))
                    .Select(au => au.UserId)
                    .ToListAsync();

                userIds.AddRange(accessUsers);

                // إرسال الإشعارات
                if (userIds.Count > 0)
                {
                    var commentUser = comment.User?.Name ?? "مستخدم";
                    await _notificationService.CreateAndSendNotificationsAsync(
                        userIds,
                        "تعليق جديد على مهمة",
                        $"المهمة رقم #{comment.TaskId}: قام {commentUser} بإضافة تعليق على المهمة '{comment.Task.Title}'",
                        "comment_added",
                        comment.TaskId
                    );
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء إشعارات التعليق: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث الشامل في تعليقات المهام - للبحث الموحد
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="userId">معرف المستخدم للتحقق من الصلاحيات (اختياري)</param>
        /// <param name="limit">عدد النتائج المطلوبة (افتراضي: 50)</param>
        /// <returns>قائمة التعليقات المطابقة</returns>
        /// <response code="200">إرجاع قائمة التعليقات المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> SearchComments(
            [FromQuery] string searchTerm,
            [FromQuery] int? userId = null,
            [FromQuery] int limit = 50)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return Ok(new List<object>());
                }

                var currentUserId = GetCurrentUserId();
                var targetUserId = userId ?? currentUserId;

                // البحث في التعليقات مع التحقق من صلاحيات الوصول للمهام
                var query = _context.TaskComments
                    .Include(tc => tc.User)
                    .Include(tc => tc.Task)
                    .ThenInclude(t => t.Creator)
                    .Include(tc => tc.Task)
                    .ThenInclude(t => t.Assignee)
                    .Where(tc => !tc.IsDeleted);

                // تطبيق فلتر الصلاحيات - التعليقات للمهام التي يمكن للمستخدم الوصول إليها
                if (targetUserId > 0)
                {
                    query = query.Where(tc =>
                        tc.Task != null && (
                            // المهام المسندة إليه
                            tc.Task.AssigneeId == targetUserId ||
                            // المهام التي أنشأها
                            tc.Task.CreatorId == targetUserId ||
                            // المهام التي له صلاحية الوصول إليها من جدول task_access_users
                            _context.TaskAccessUsers.Any(tau => tau.TaskId == tc.TaskId && tau.UserId == targetUserId)
                        )
                    );
                }

                // البحث في محتوى التعليقات
                query = query.Where(tc =>
                    // البحث في محتوى التعليق
                    tc.Content.Contains(searchTerm) ||
                    // البحث في اسم كاتب التعليق
                    (tc.User != null && tc.User.Name.Contains(searchTerm)) ||
                    // البحث في عنوان المهمة
                    (tc.Task != null && tc.Task.Title.Contains(searchTerm))
                );

                // تطبيق الحد الأقصى للنتائج والترتيب
                var comments = await query
                    .OrderByDescending(tc => tc.CreatedAt)
                    .Take(Math.Min(limit, 100)) // حد أقصى 100 نتيجة
                    .Select(tc => new {
                        tc.Id,
                        tc.Content,
                        tc.CreatedAt,
                        tc.TaskId,
                        TaskTitle = tc.Task != null ? tc.Task.Title : null,
                        AuthorName = tc.User != null ? tc.User.Name : null,
                        tc.UserId
                    })
                    .ToListAsync();

                _logger.LogInformation("البحث في التعليقات: تم العثور على {Count} نتيجة للمصطلح '{SearchTerm}'",
                    comments.Count, searchTerm);

                return Ok(comments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث في التعليقات بالمصطلح: {SearchTerm}", searchTerm);
                return StatusCode(500, "خطأ في الخادم أثناء البحث");
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي من JWT token
        /// </summary>
        private int GetCurrentUserId()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value ??
                                 User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }
    }
}
